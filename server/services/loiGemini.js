const https = require('https');
const { URL } = require('url');

class LoIGeminiService {
  constructor() {
    this.client = null;
    this.initPromise = this.initializeClient();
  }

  async initializeClient() {
    if (!this.client) {
      const { GoogleGenAI } = await import('@google/genai');
      this.client = new GoogleGenAI({
        apiKey: process.env.GEMINI_API_KEY,
        httpOptions: {
          timeout: 600000
        }
      });
    }
    return this.client;
  }

  async generateResponse(model, prompt, config) {
    return new Promise((resolve, reject) => {
      try {
        const requestUrl = `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent`;
        const queryParams = `key=${process.env.GEMINI_API_KEY}`;
        const url = new URL(`${requestUrl}?${queryParams}`);

        let generationConfig = {
          temperature: config.temperature,
          responseMimeType: config.responseMimeType
        };

        if (config.thinkingConfig) {
          generationConfig.thinkingConfig = config.thinkingConfig;
        }

        const postData = JSON.stringify({
          system_instruction: { parts: [{ text: config.systemInstruction }] },
          contents: [{ parts: [{ text: prompt }] }],
          generationConfig: generationConfig
        });

        const options = {
          hostname: url.hostname,
          port: url.port || 443,
          path: url.pathname + url.search,
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(postData)
          },
          timeout: 600000 // 10 minutes timeout
        };

        const req = https.request(options, (res) => {
          let data = '';
          res.on('data', (chunk) => { data += chunk; });

          res.on('end', () => {
            try {
              if (res.statusCode !== 200) {
                reject(new Error(`HTTP error! status: ${res.statusCode}`));
                return;
              }

              const responseData = JSON.parse(data);
              resolve(responseData.candidates[0].content.parts[0].text);
            } catch (parseError) {
              console.error('Error parsing response:', parseError);
              reject(new Error('Failed to parse response from Gemini'));
            }
          });
        });

        req.on('error', (error) => {
          console.error('Request error:', error);
          reject(new Error('Failed to generate response from Gemini'));
        });

        req.on('timeout', () => {
          req.destroy();
          reject(new Error('Request timeout - Gemini API took too long to respond'));
        });

        req.setTimeout(600000); // 10 minutes timeout
        req.write(postData);
        req.end();

      } catch (error) {
        console.error('Error generating response:', error);
        reject(new Error('Failed to generate response from Gemini'));
      }
    });
  }

  // Calculate the highest level with check=true for a competency
  getCompetencyScore(competencyData) {
    if (!competencyData || !competencyData.levels) return 0;
    
    let highestScore = 0;
    for (const level of competencyData.levels) {
      if (level.check === true && level.order > highestScore) {
        highestScore = level.order;
      }
    }
    return highestScore;
  }

  // Parse expected results from CSV format
  parseExpectedResults(expectedResultsStr) {
    try {
      const expectedResults = JSON.parse(expectedResultsStr);
      const continuousLearning = expectedResults.find(comp => comp.competency === "Continuous Learning");
      const drivingForResult = expectedResults.find(comp => comp.competency === "Driving for Result");

      return {
        continuousLearningScore: this.getCompetencyScore(continuousLearning),
        drivingForResultScore: this.getCompetencyScore(drivingForResult)
      };
    } catch (error) {
      console.error('Error parsing expected results:', error);
      return { continuousLearningScore: 0, drivingForResultScore: 0 };
    }
  }

  // Clean Gemini response by removing markdown and common formatting issues
  cleanGeminiResponse(response) {
    let cleaned = response.trim();

    // Remove markdown code block markers
    if (cleaned.startsWith('```json')) {
      cleaned = cleaned.replace(/^```json\s*/, '');
    }
    if (cleaned.endsWith('```')) {
      cleaned = cleaned.replace(/\s*```$/, '');
    }

    // Remove any leading/trailing whitespace again
    cleaned = cleaned.trim();

    return cleaned;
  }

  // More aggressive cleaning for malformed JSON responses
  aggressivelyCleanResponse(response) {
    let cleaned = this.cleanGeminiResponse(response);

    try {
      // Try to find the JSON object boundaries
      const startIndex = cleaned.indexOf('{');
      const lastIndex = cleaned.lastIndexOf('}');

      if (startIndex !== -1 && lastIndex !== -1 && lastIndex > startIndex) {
        cleaned = cleaned.substring(startIndex, lastIndex + 1);
      }

      // Remove any text that appears after "check": true/false but before the next valid JSON token
      // This handles cases like: "check": true immunotherapy
      cleaned = cleaned.replace(/("check":\s*(true|false))\s+[a-zA-Z][a-zA-Z0-9]*(?=\s*[,}])/g, '$1');

      // Remove any standalone words that appear between JSON tokens
      // This is a more general approach to catch stray text
      cleaned = cleaned.replace(/([,{]\s*)[a-zA-Z][a-zA-Z0-9]*\s*(?=[,}])/g, '$1');

      // Fix any double commas that might result from cleaning
      cleaned = cleaned.replace(/,\s*,/g, ',');

      // Fix any trailing commas before closing braces/brackets
      cleaned = cleaned.replace(/,\s*([}\]])/g, '$1');

    } catch (error) {
      console.error('Error in aggressive cleaning:', error);
      // Return the basic cleaned version if aggressive cleaning fails
      return this.cleanGeminiResponse(response);
    }

    return cleaned;
  }

  async runLoIEvaluation(datasetRows, promptContent) {
    try {
      await this.initPromise; // Ensure client is initialized

      const results = [];
      let continuousLearningCorrect = 0;
      let drivingForResultCorrect = 0;
      const totalRows = datasetRows.length;

      console.log(`Starting LoI evaluation for ${totalRows} rows...`);

      for (let i = 0; i < datasetRows.length; i++) {
        const row = datasetRows[i];
        const inputText = row.input_text;
        const expectedResults = this.parseExpectedResults(row.expected_results);

        console.log(`Processing row ${i + 1}/${totalRows}...`);

        // Replace {{text}} in prompt with actual input text
        const processedPrompt = promptContent.replace('{{text}}', inputText);

        const config = {
          temperature: 0,
          responseMimeType: "application/json",
          systemInstruction: processedPrompt,
          thinkingConfig: { thinkingBudget: -1 }
        };

        try {
          const geminiResponse = await this.generateResponse(
            'gemini-2.5-flash',
            inputText, // The user prompt is just the input text
            config
          );

          // Log the raw response for debugging
          console.log(`Raw Gemini response for row ${i + 1}:`, geminiResponse);

          // Check if gemini response already an object or stil json string
          if (typeof geminiResponse === 'object') {
            console.log(`Gemini response for row ${i + 1} is already an object.`);
          } else {
            console.log(`Gemini response for row ${i + 1} is a string.`);
          }

          // Parse Gemini response with better error handling
          let parsedResponse;

          // If it already object, we can skip cleaning
          if (typeof geminiResponse === 'object') {
            parsedResponse = geminiResponse;
          } else {
            // Clean up the response with more robust cleaning
            let cleanedResponse = this.cleanGeminiResponse(geminiResponse);
            try {
              parsedResponse = JSON.parse(cleanedResponse);
            } catch (jsonError) {
              console.error(`JSON parsing error for row ${i + 1}:`, jsonError.message);
              console.error(`Raw response that failed to parse:`, geminiResponse);
              console.error(`Cleaned response that failed to parse:`, cleanedResponse);

              // Try one more aggressive cleaning attempt
              const aggressivelyCleanedResponse = this.aggressivelyCleanResponse(geminiResponse);
              try {
                parsedResponse = JSON.parse(aggressivelyCleanedResponse);
                console.log(`Successfully parsed after aggressive cleaning for row ${i + 1}`);
              } catch (secondError) {
                console.error(`Even aggressive cleaning failed for row ${i + 1}:`, secondError.message);
                throw new Error(`Invalid JSON response from Gemini: ${jsonError.message}`);
              }
            }
          }

          const continuousLearning = parsedResponse.results?.find(comp => comp.competency === "Continuous Learning");
          const drivingForResult = parsedResponse.results?.find(comp => comp.competency === "Driving for result");

          const actualScores = {
            continuousLearningScore: this.getCompetencyScore(continuousLearning),
            drivingForResultScore: this.getCompetencyScore(drivingForResult)
          };

          // Calculate differences and check if correct
          const continuousLearningDiff = actualScores.continuousLearningScore - expectedResults.continuousLearningScore;
          const drivingForResultDiff = actualScores.drivingForResultScore - expectedResults.drivingForResultScore;

          if (continuousLearningDiff === 0) continuousLearningCorrect++;
          if (drivingForResultDiff === 0) drivingForResultCorrect++;

          results.push({
            rowIndex: i + 1,
            inputText: inputText,
            expectedContinuousLearning: expectedResults.continuousLearningScore,
            actualContinuousLearning: actualScores.continuousLearningScore,
            continuousLearningDiff: continuousLearningDiff,
            expectedDrivingForResult: expectedResults.drivingForResultScore,
            actualDrivingForResult: actualScores.drivingForResultScore,
            drivingForResultDiff: drivingForResultDiff,
            geminiResponse: parsedResponse,
            rawResponse: geminiResponse
          });

        } catch (error) {
          console.error(`Error processing row ${i + 1}:`, error);
          results.push({
            rowIndex: i + 1,
            inputText: inputText,
            expectedContinuousLearning: expectedResults.continuousLearningScore,
            actualContinuousLearning: 0,
            continuousLearningDiff: -expectedResults.continuousLearningScore,
            expectedDrivingForResult: expectedResults.drivingForResultScore,
            actualDrivingForResult: 0,
            drivingForResultDiff: -expectedResults.drivingForResultScore,
            error: error.message,
            geminiResponse: null,
            rawResponse: null
          });
        }
      }

      // Calculate accuracy percentages
      const continuousLearningAccuracy = totalRows > 0 ? continuousLearningCorrect / totalRows : 0;
      const drivingForResultAccuracy = totalRows > 0 ? drivingForResultCorrect / totalRows : 0;

      console.log(`LoI evaluation completed. Continuous Learning accuracy: ${(continuousLearningAccuracy * 100).toFixed(1)}%, Driving for Result accuracy: ${(drivingForResultAccuracy * 100).toFixed(1)}%`);

      return {
        results: results,
        summary: {
          totalRows: totalRows,
          continuousLearningCorrect: continuousLearningCorrect,
          continuousLearningAccuracy: continuousLearningAccuracy,
          drivingForResultCorrect: drivingForResultCorrect,
          drivingForResultAccuracy: drivingForResultAccuracy
        }
      };

    } catch (error) {
      console.error('Error in LoI evaluation:', error);
      throw error;
    }
  }
}

module.exports = new LoIGeminiService();

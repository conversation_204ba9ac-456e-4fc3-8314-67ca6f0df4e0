const fs = require('fs').promises;
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '..', '.env') });
const supabase = require('../supabaseClient');

async function updateLoIPrompt() {
  try {
    console.log('Updating LoI Scoring Prompt in database...\n');

    // Read the updated prompt content from file
    const promptPath = path.join(__dirname, '../data/loi_prompt.txt');
    const promptContent = await fs.readFile(promptPath, 'utf8');

    // Update the prompt
    const { data: updatedPrompt, error: updateError } = await supabase
      .from('prompts')
      .update({
        content: promptContent,
        version: 2,
        updatedAt: new Date().toISOString()
      })
      .eq('id', 16)
      .select('*')
      .single();

    if (updateError) {
      console.error('Error updating LoI prompt:', updateError);
      return;
    }

    if (!updatedPrompt) {
      console.error('❌ LoI prompt not found in database (ID: 16)');
      return;
    }

    console.log('✅ LoI Scoring Prompt updated successfully!');
    console.log(`  - ID: ${updatedPrompt.id}`);
    console.log(`  - Name: ${updatedPrompt.name}`);
    console.log(`  - Version: ${updatedPrompt.version}`);
    console.log(`  - Content length: ${updatedPrompt.content.length} characters`);
    console.log(`  - Updated at: ${updatedPrompt.updatedAt}`);

    // Verify the updated content contains the new evidences field
    if (updatedPrompt.content.includes('"evidences": String[]')) {
      console.log('✅ Prompt now includes evidences field in the output format');
    } else {
      console.log('⚠️  Warning: Prompt may not include the evidences field');
    }

  } catch (error) {
    console.error('Error updating LoI prompt:', error);
  }
}

// Run the update
updateLoIPrompt().then(() => {
  console.log('\nUpdate completed.');
  process.exit(0);
}).catch(error => {
  console.error('Update failed:', error);
  process.exit(1);
});

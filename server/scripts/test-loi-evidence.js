const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '..', '.env') });
const supabase = require('../supabaseClient');

async function createTestLoIEvaluationWithEvidence() {
  try {
    console.log('Creating test LoI evaluation with evidence data...\n');

    // Mock evaluation result with the new evidence format
    const mockResult = {
      results: [
        {
          rowIndex: 1,
          inputText: "I actively seek feedback from my manager and colleagues to improve my performance. I recently completed an online course on data analysis to enhance my skills for my current project.",
          expectedContinuousLearning: 2,
          actualContinuousLearning: 2,
          continuousLearningDiff: 0,
          expectedDrivingForResult: 1,
          actualDrivingForResult: 1,
          drivingForResultDiff: 0,
          geminiResponse: {
            results: [
              {
                competency: "Continuous Learning",
                levels: [
                  {
                    level: "Level 1",
                    order: 1,
                    evidences: ["Shows interest in learning by actively seeking feedback from manager and colleagues"],
                    check: true
                  },
                  {
                    level: "Level 2", 
                    order: 2,
                    evidences: ["Seeks new information independently by completing an online course on data analysis", "Applies new knowledge to improve work quality for current project"],
                    check: true
                  },
                  {
                    level: "Level 3",
                    order: 3,
                    evidences: [],
                    check: false
                  },
                  {
                    level: "Level 4",
                    order: 4,
                    evidences: [],
                    check: false
                  },
                  {
                    level: "Level 5",
                    order: 5,
                    evidences: [],
                    check: false
                  }
                ]
              },
              {
                competency: "Driving for result",
                levels: [
                  {
                    level: "Level 1",
                    order: 1,
                    evidences: ["Completes tasks by taking initiative to improve performance through feedback"],
                    check: true
                  },
                  {
                    level: "Level 2",
                    order: 2,
                    evidences: [],
                    check: false
                  },
                  {
                    level: "Level 3",
                    order: 3,
                    evidences: [],
                    check: false
                  },
                  {
                    level: "Level 4",
                    order: 4,
                    evidences: [],
                    check: false
                  },
                  {
                    level: "Level 5",
                    order: 5,
                    evidences: [],
                    check: false
                  }
                ]
              }
            ]
          },
          rawResponse: "{\"results\":[...]}"
        }
      ],
      summary: {
        totalRows: 1,
        continuousLearningCorrect: 1,
        continuousLearningAccuracy: 1.0,
        drivingForResultCorrect: 1,
        drivingForResultAccuracy: 1.0
      }
    };

    // Insert test evaluation
    const { data: evaluation, error: insertError } = await supabase
      .from('loi_evaluations')
      .insert([{
        dataset_name: 'Test Dataset with Evidence',
        prompt_version: 2,
        prompt_content: 'Test prompt content with evidence support',
        status: 'completed',
        output: mockResult,
        details: mockResult,
        continuous_learning_accuracy: mockResult.summary.continuousLearningAccuracy,
        driving_for_result_accuracy: mockResult.summary.drivingForResultAccuracy,
        total_rows_processed: mockResult.summary.totalRows,
        timestamp: new Date().toISOString()
      }])
      .select('*')
      .single();

    if (insertError) {
      console.error('Error inserting test evaluation:', insertError);
      return;
    }

    console.log('✅ Test LoI evaluation with evidence created successfully!');
    console.log(`  - ID: ${evaluation.id}`);
    console.log(`  - Dataset: ${evaluation.dataset_name}`);
    console.log(`  - Status: ${evaluation.status}`);
    console.log(`  - CL Accuracy: ${(evaluation.continuous_learning_accuracy * 100).toFixed(1)}%`);
    console.log(`  - DR Accuracy: ${(evaluation.driving_for_result_accuracy * 100).toFixed(1)}%`);
    console.log(`  - Rows Processed: ${evaluation.total_rows_processed}`);
    
    console.log('\n✅ You can now test the evidence modal by:');
    console.log('1. Going to the LoI Scoring Evaluations page');
    console.log('2. Clicking "View Details" on the test evaluation');
    console.log('3. Clicking on the green "2" or "1" buttons in the Actual CL/DR columns');
    console.log('4. The evidence modal should show the specific evidence for each level');

  } catch (error) {
    console.error('Error creating test evaluation:', error);
  }
}

// Run the test
createTestLoIEvaluationWithEvidence().then(() => {
  console.log('\nTest data creation completed.');
  process.exit(0);
}).catch(error => {
  console.error('Test data creation failed:', error);
  process.exit(1);
});

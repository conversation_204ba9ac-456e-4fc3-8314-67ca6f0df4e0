import React, { useState, useEffect } from 'react';
import PromptEditor from './components/PromptEditor';
import EvaluationRunner from './components/EvaluationRunner';
import LGDEvaluationRunner from './components/LGDEvaluationRunner';
import BEIEvaluationRunner from './components/BEIEvaluationRunner';
import EtrayEvaluationRunner from './components/EtrayEvaluationRunner';
import AIInterviewEvaluationRunner from './components/AIInterviewEvaluationRunner';
import AIInterviewV2EvaluationRunner from './components/AIInterviewV2EvaluationRunner';
import EnglishProficiencyEvaluationRunner from './components/EnglishProficiencyEvaluationRunner';
import LoIEvaluationRunner from './components/LoIEvaluationRunner';
import DatasetManager from './components/DatasetManager';
import ResultsTable from './components/ResultsTable';
import LGDResultsTable from './components/LGDResultsTable';
import BEIResultsTable from './components/BEIResultsTable';
import EtrayResultsTable from './components/EtrayResultsTable';
import AIInterviewResultsTable from './components/AIInterviewResultsTable';
import AIInterviewV2ResultsTable from './components/AIInterviewV2ResultsTable';
import EnglishProficiencyResultsTable from './components/EnglishProficiencyResultsTable';
import LoIResultsTable from './components/LoIResultsTable';
import Sidebar from './components/Sidebar';
import { promptsApi, evaluationsApi, lgdEvaluationsApi, beiEvaluationsApi, etrayEvaluationsApi, aiInterviewEvaluationsApi, aiInterviewV2EvaluationsApi, englishProficiencyEvaluationsApi, loiEvaluationsApi } from './services/api';

const App = () => {
  const [prompts, setPrompts] = useState([]);
  const [evaluations, setEvaluations] = useState([]);
  const [lgdEvaluations, setLgdEvaluations] = useState([]);
  const [beiEvaluations, setBeiEvaluations] = useState([]);
  const [etrayEvaluations, setEtrayEvaluations] = useState([]);
  const [aiInterviewEvaluations, setAiInterviewEvaluations] = useState([]);
  const [aiInterviewV2Evaluations, setAiInterviewV2Evaluations] = useState([]);
  const [englishProficiencyEvaluations, setEnglishProficiencyEvaluations] = useState([]);
  const [loiEvaluations, setLoiEvaluations] = useState([]);
  const [selectedDatasetId, setSelectedDatasetId] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeEvalType, setActiveEvalType] = useState('idp'); // 'idp', 'lgd', 'bei', 'etray', 'ai-interview', 'ai-interview-v2', 'english-proficiency', or 'loi'

  // Track which data has been loaded to avoid refetching
  const [loadedData, setLoadedData] = useState({
    prompts: false,
    idp: false,
    lgd: false,
    bei: false,
    etray: false,
    aiInterview: false,
    aiInterviewV2: false,
    englishProficiency: false,
    loi: false
  });

  useEffect(() => {
    loadInitialData();
  }, []);

  // Load prompts and IDP data on initial load (since IDP is the default tab)
  const loadInitialData = async () => {
    try {
      setLoading(true);
      const [promptsResponse, evaluationsResponse] = await Promise.all([
        promptsApi.getAll(),
        evaluationsApi.getAll()
      ]);

      setPrompts(promptsResponse.data);
      setEvaluations(evaluationsResponse.data);
      setLoadedData(prev => ({ ...prev, prompts: true, idp: true }));
      setError(null);
    } catch (err) {
      setError('Failed to load data');
      console.error('Error loading initial data:', err);
    } finally {
      setLoading(false);
    }
  };

  // Load LGD evaluations when needed
  const loadLGDData = async () => {
    if (loadedData.lgd) return; // Already loaded

    try {
      const lgdEvaluationsResponse = await lgdEvaluationsApi.getAll();
      setLgdEvaluations(lgdEvaluationsResponse.data);
      setLoadedData(prev => ({ ...prev, lgd: true }));
    } catch (lgdError) {
      console.warn('LGD evaluations not available yet:', lgdError);
      setLgdEvaluations([]);
      setLoadedData(prev => ({ ...prev, lgd: true })); // Mark as loaded even if empty
    }
  };

  // Load BEI evaluations when needed
  const loadBEIData = async () => {
    if (loadedData.bei) return; // Already loaded

    try {
      const beiEvaluationsResponse = await beiEvaluationsApi.getAll();
      setBeiEvaluations(beiEvaluationsResponse.data);
      setLoadedData(prev => ({ ...prev, bei: true }));
    } catch (beiError) {
      console.warn('BEI evaluations not available yet:', beiError);
      setBeiEvaluations([]);
      setLoadedData(prev => ({ ...prev, bei: true })); // Mark as loaded even if empty
    }
  };

  // Load E-tray evaluations when needed
  const loadEtrayData = async () => {
    if (loadedData.etray) return; // Already loaded

    try {
      const etrayEvaluationsResponse = await etrayEvaluationsApi.getAll();
      setEtrayEvaluations(etrayEvaluationsResponse.data);
      setLoadedData(prev => ({ ...prev, etray: true }));
    } catch (etrayError) {
      console.warn('E-tray evaluations not available yet:', etrayError);
      setEtrayEvaluations([]);
      setLoadedData(prev => ({ ...prev, etray: true })); // Mark as loaded even if empty
    }
  };

  // Load AI Interview evaluations when needed
  const loadAIInterviewData = async () => {
    if (loadedData.aiInterview) return; // Already loaded

    try {
      const aiInterviewEvaluationsResponse = await aiInterviewEvaluationsApi.getAll();
      setAiInterviewEvaluations(aiInterviewEvaluationsResponse.data);
      setLoadedData(prev => ({ ...prev, aiInterview: true }));
    } catch (aiInterviewError) {
      console.warn('AI Interview evaluations not available yet:', aiInterviewError);
      setAiInterviewEvaluations([]);
      setLoadedData(prev => ({ ...prev, aiInterview: true })); // Mark as loaded even if empty
    }
  };

  // Load AI Interview V2 evaluations when needed
  const loadAIInterviewV2Data = async () => {
    if (loadedData.aiInterviewV2) return; // Already loaded

    try {
      const aiInterviewV2EvaluationsResponse = await aiInterviewV2EvaluationsApi.getAll();
      setAiInterviewV2Evaluations(aiInterviewV2EvaluationsResponse.data);
      setLoadedData(prev => ({ ...prev, aiInterviewV2: true }));
    } catch (aiInterviewV2Error) {
      console.warn('AI Interview V2 evaluations not available yet:', aiInterviewV2Error);
      setAiInterviewV2Evaluations([]);
      setLoadedData(prev => ({ ...prev, aiInterviewV2: true })); // Mark as loaded even if empty
    }
  };

  // Load English Proficiency evaluations when needed
  const loadEnglishProficiencyData = async () => {
    if (loadedData.englishProficiency) return; // Already loaded

    try {
      const englishProficiencyEvaluationsResponse = await englishProficiencyEvaluationsApi.getAll();
      setEnglishProficiencyEvaluations(englishProficiencyEvaluationsResponse.data);
      setLoadedData(prev => ({ ...prev, englishProficiency: true }));
    } catch (englishProficiencyError) {
      console.warn('English Proficiency evaluations not available yet:', englishProficiencyError);
      setEnglishProficiencyEvaluations([]);
      setLoadedData(prev => ({ ...prev, englishProficiency: true })); // Mark as loaded even if empty
    }
  };

  // Load LoI evaluations when needed
  const loadLoIData = async () => {
    if (loadedData.loi) return; // Already loaded

    try {
      const loiEvaluationsResponse = await loiEvaluationsApi.getAll();
      setLoiEvaluations(loiEvaluationsResponse.data);
      setLoadedData(prev => ({ ...prev, loi: true }));
    } catch (loiError) {
      console.warn('LoI evaluations not available yet:', loiError);
      setLoiEvaluations([]);
      setLoadedData(prev => ({ ...prev, loi: true })); // Mark as loaded even if empty
    }
  };

  // Handle tab switching with lazy loading
  const handleEvalTypeChange = async (newEvalType) => {
    setActiveEvalType(newEvalType);

    // Load data for the new tab if not already loaded
    if (newEvalType === 'lgd' && !loadedData.lgd) {
      await loadLGDData();
    } else if (newEvalType === 'bei' && !loadedData.bei) {
      await loadBEIData();
    } else if (newEvalType === 'etray' && !loadedData.etray) {
      await loadEtrayData();
    } else if (newEvalType === 'ai-interview' && !loadedData.aiInterview) {
      await loadAIInterviewData();
    } else if (newEvalType === 'ai-interview-v2' && !loadedData.aiInterviewV2) {
      await loadAIInterviewV2Data();
    } else if (newEvalType === 'english-proficiency' && !loadedData.englishProficiency) {
      await loadEnglishProficiencyData();
    } else if (newEvalType === 'loi' && !loadedData.loi) {
      await loadLoIData();
    }
  };

  const handlePromptUpdate = async (id, content) => {
    try {
      const response = await promptsApi.update(id, content);
      setPrompts(prev => prev.map(p => p.id === id ? response.data : p));
      return response.data;
    } catch (err) {
      console.error('Error updating prompt:', err);
      throw err;
    }
  };

  const handleEvaluationRun = async (input) => {
    try {
      const response = await evaluationsApi.run(input);
      setEvaluations(prev => [response.data, ...prev]);
      return response.data;
    } catch (err) {
      console.error('Error running evaluation:', err);
      throw err;
    }
  };

  const handleLGDEvaluationRun = async (input) => {
    try {
      const response = await lgdEvaluationsApi.run(input);
      setLgdEvaluations(prev => [response.data, ...prev]);
      return response.data;
    } catch (err) {
      console.error('Error running LGD evaluation:', err);
      throw err;
    }
  };

  const handleBEIEvaluationRun = async (input) => {
    try {
      const response = await beiEvaluationsApi.run(input);
      setBeiEvaluations(prev => [response.data, ...prev]);
      return response.data;
    } catch (err) {
      console.error('Error running BEI evaluation:', err);
      throw err;
    }
  };

  const handleEtrayEvaluationRun = async (formData) => {
    try {
      const response = await etrayEvaluationsApi.run(formData);
      setEtrayEvaluations(prev => [response.data, ...prev]);
      return response.data;
    } catch (err) {
      console.error('Error running E-tray evaluation:', err);
      throw err;
    }
  };

  const handleAIInterviewEvaluationRun = async (datasetId) => {
    try {
      const response = await aiInterviewEvaluationsApi.run(datasetId);
      const newEvaluation = response.data;

      // Add to the beginning of the list
      setAiInterviewEvaluations(prev => [newEvaluation, ...prev]);

      return newEvaluation;
    } catch (error) {
      console.error('Error running AI Interview evaluation:', error);
      throw error;
    }
  };

  const handleAIInterviewV2EvaluationRun = async (datasetId, transcribeModel, transcribeTemperature, analysisModel, analysisTemperature) => {
    try {
      const response = await aiInterviewV2EvaluationsApi.run(datasetId, transcribeModel, transcribeTemperature, analysisModel, analysisTemperature);
      const newEvaluation = response.data;

      // Add to the beginning of the list
      setAiInterviewV2Evaluations(prev => [newEvaluation, ...prev]);

      return newEvaluation;
    } catch (error) {
      console.error('Error running AI Interview V2 evaluation:', error);
      throw error;
    }
  };

  const handleEnglishProficiencyEvaluationRun = async (datasetId, transcribeModel, transcribeTemperature, analysisModel, analysisTemperature) => {
    try {
      const response = await englishProficiencyEvaluationsApi.run(datasetId, transcribeModel, transcribeTemperature, analysisModel, analysisTemperature);
      const newEvaluation = response.data;

      // Add to the beginning of the list
      setEnglishProficiencyEvaluations(prev => [newEvaluation, ...prev]);

      return newEvaluation;
    } catch (error) {
      console.error('Error running English Proficiency evaluation:', error);
      throw error;
    }
  };

  const handleLoIEvaluationRun = async (datasetId) => {
    try {
      const response = await loiEvaluationsApi.run(datasetId);
      const newEvaluation = response.data;

      // Add to the beginning of the list
      setLoiEvaluations(prev => [newEvaluation, ...prev]);

      return newEvaluation;
    } catch (error) {
      console.error('Error running LoI evaluation:', error);
      throw error;
    }
  };

  const handleLGDPollingUpdate = async (evaluationId, status) => {
    try {
      // Refresh the specific evaluation or all evaluations
      const response = await lgdEvaluationsApi.getAll();
      setLgdEvaluations(response.data);

      if (status === 'completed') {
        console.log(`LGD evaluation ${evaluationId} completed successfully`);
      } else if (status === 'error') {
        console.error(`LGD evaluation ${evaluationId} failed`);
      }
    } catch (err) {
      console.error('Error refreshing LGD evaluations after polling:', err);
    }
  };

  const handleBEIPollingUpdate = async (evaluationId, status) => {
    try {
      // Refresh the specific evaluation or all evaluations
      const response = await beiEvaluationsApi.getAll();
      setBeiEvaluations(response.data);

      if (status === 'completed') {
        console.log(`BEI evaluation ${evaluationId} completed successfully`);
      } else if (status === 'error') {
        console.error(`BEI evaluation ${evaluationId} failed`);
      }
    } catch (err) {
      console.error('Error refreshing BEI evaluations after polling:', err);
    }
  };

  const handleEtrayPollingUpdate = async (evaluationId, status) => {
    try {
      // Refresh the specific evaluation or all evaluations
      const response = await etrayEvaluationsApi.getAll();
      setEtrayEvaluations(response.data);

      if (status === 'completed') {
        console.log(`E-tray evaluation ${evaluationId} completed successfully`);
      } else if (status === 'error') {
        console.error(`E-tray evaluation ${evaluationId} failed`);
      }
    } catch (err) {
      console.error('Error refreshing E-tray evaluations after polling:', err);
    }
  };

  const handleEvaluationUpdate = (updatedEvaluation) => {
    setEvaluations(prev =>
      prev.map(evaluation => evaluation.id === updatedEvaluation.id ? updatedEvaluation : evaluation)
    );
  };

  const handleLGDEvaluationUpdate = (updatedEvaluation) => {
    setLgdEvaluations(prev =>
      prev.map(evaluation => evaluation.id === updatedEvaluation.id ? updatedEvaluation : evaluation)
    );
  };

  const handleBEIEvaluationUpdate = (updatedEvaluation) => {
    setBeiEvaluations(prev =>
      prev.map(evaluation => evaluation.id === updatedEvaluation.id ? updatedEvaluation : evaluation)
    );
  };

  const handleEtrayEvaluationUpdate = (updatedEvaluation) => {
    setEtrayEvaluations(prev =>
      prev.map(evaluation => evaluation.id === updatedEvaluation.id ? updatedEvaluation : evaluation)
    );
  };

  const handleAIInterviewPollingUpdate = async (updatedEvaluation) => {
    try {
      // Refresh all AI Interview evaluations when polling stops
      const response = await aiInterviewEvaluationsApi.getAll();
      setAiInterviewEvaluations(response.data);

      if (updatedEvaluation.status === 'completed') {
        console.log(`AI Interview evaluation ${updatedEvaluation.id} completed successfully`);
      } else if (updatedEvaluation.status === 'error') {
        console.error(`AI Interview evaluation ${updatedEvaluation.id} failed`);
      }
    } catch (err) {
      console.error('Error refreshing AI Interview evaluations after polling:', err);
      // Fallback to updating just the single evaluation if API call fails
      setAiInterviewEvaluations(prev =>
        prev.map(evaluation => evaluation.id === updatedEvaluation.id ? updatedEvaluation : evaluation)
      );
    }
  };

  const handleAIInterviewEvaluationUpdate = (updatedEvaluation) => {
    setAiInterviewEvaluations(prev =>
      prev.map(evaluation => evaluation.id === updatedEvaluation.id ? updatedEvaluation : evaluation)
    );
  };

  const handleAIInterviewV2PollingUpdate = async (updatedEvaluation) => {
    try {
      // Refresh all AI Interview V2 evaluations when polling stops
      const response = await aiInterviewV2EvaluationsApi.getAll();
      setAiInterviewV2Evaluations(response.data);

      if (updatedEvaluation.status === 'completed') {
        console.log(`AI Interview V2 evaluation ${updatedEvaluation.id} completed successfully`);
      } else if (updatedEvaluation.status === 'error') {
        console.error(`AI Interview V2 evaluation ${updatedEvaluation.id} failed`);
      }
    } catch (err) {
      console.error('Error refreshing AI Interview V2 evaluations after polling:', err);
      // Fallback to updating just the single evaluation if API call fails
      setAiInterviewV2Evaluations(prev =>
        prev.map(evaluation => evaluation.id === updatedEvaluation.id ? updatedEvaluation : evaluation)
      );
    }
  };

  const handleAIInterviewV2EvaluationUpdate = (updatedEvaluation) => {
    setAiInterviewV2Evaluations(prev =>
      prev.map(evaluation => evaluation.id === updatedEvaluation.id ? updatedEvaluation : evaluation)
    );
  };

  const handleEnglishProficiencyPollingUpdate = async (updatedEvaluation) => {
    try {
      // Refresh all English Proficiency evaluations when polling stops
      const response = await englishProficiencyEvaluationsApi.getAll();
      setEnglishProficiencyEvaluations(response.data);

      if (updatedEvaluation.status === 'completed') {
        console.log(`English Proficiency evaluation ${updatedEvaluation.id} completed successfully`);
      } else if (updatedEvaluation.status === 'error') {
        console.error(`English Proficiency evaluation ${updatedEvaluation.id} failed`);
      }
    } catch (err) {
      console.error('Error refreshing English Proficiency evaluations after polling:', err);
      // Fallback to updating just the single evaluation if API call fails
      setEnglishProficiencyEvaluations(prev =>
        prev.map(evaluation => evaluation.id === updatedEvaluation.id ? updatedEvaluation : evaluation)
      );
    }
  };

  const handleEnglishProficiencyEvaluationUpdate = (updatedEvaluation) => {
    setEnglishProficiencyEvaluations(prev =>
      prev.map(evaluation => evaluation.id === updatedEvaluation.id ? updatedEvaluation : evaluation)
    );
  };

  const handleLoIPollingUpdate = async (updatedEvaluation) => {
    try {
      // Refresh all LoI evaluations when polling stops
      const response = await loiEvaluationsApi.getAll();
      setLoiEvaluations(response.data);

      if (updatedEvaluation.status === 'completed') {
        console.log(`LoI evaluation ${updatedEvaluation.id} completed successfully`);
      } else if (updatedEvaluation.status === 'error') {
        console.error(`LoI evaluation ${updatedEvaluation.id} failed`);
      }
    } catch (err) {
      console.error('Error refreshing LoI evaluations after polling:', err);
      // Fallback to updating just the single evaluation if API call fails
      setLoiEvaluations(prev =>
        prev.map(evaluation => evaluation.id === updatedEvaluation.id ? updatedEvaluation : evaluation)
      );
    }
  };



  if (loading) {
    return (
      <div style={{ padding: '20px', textAlign: 'center' }}>
        <h2>Loading...</h2>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ padding: '20px', textAlign: 'center', color: 'red' }}>
        <h2>Error: {error}</h2>
        <button onClick={loadInitialData}>Retry</button>
      </div>
    );
  }

  // Filter prompts based on active evaluation type
  const getFilteredPrompts = () => {
    if (activeEvalType === 'idp') {
      return prompts.filter(p => p.id <= 2); // IDP prompts (1, 2)
    } else if (activeEvalType === 'lgd') {
      return prompts.filter(p => p.id === 3); // LGD prompts (only 3, hide 4)
    } else if (activeEvalType === 'bei') {
      return []; // BEI uses a fixed prompt from file system, no editable prompts
    } else if (activeEvalType === 'etray') {
      return []; // E-tray uses fixed prompts from database, no editable prompts in main view
    } else if (activeEvalType === 'ai-interview') {
      return prompts.filter(p => p.id >= 5 && p.id <= 7); // AI Interview prompts (5, 6, 7)
    } else if (activeEvalType === 'ai-interview-v2') {
      return prompts.filter(p => p.id >= 12 && p.id <= 13); // AI Interview V2 prompts (12, 13)
    } else if (activeEvalType === 'english-proficiency') {
      return prompts.filter(p => p.id >= 14 && p.id <= 15); // English Proficiency prompts (14, 15)
    } else if (activeEvalType === 'loi') {
      return prompts.filter(p => p.id === 16); // LoI Scoring prompt (16)
    }
    return [];
  };

  return (
    <div style={{ display: 'flex', minHeight: '100vh' }}>
      <Sidebar
        activeEvalType={activeEvalType}
        onEvalTypeChange={handleEvalTypeChange}
      />

      <div style={{ flex: 1, padding: '20px', maxWidth: '1400px', margin: '0 auto' }}>
        <h1 style={{ textAlign: 'center', marginBottom: '30px' }}>
          {activeEvalType === 'idp' ? 'IDP Recommendation' :
           activeEvalType === 'lgd' ? 'LGD Analysis' :
           activeEvalType === 'bei' ? 'BEI Analysis' :
           activeEvalType === 'etray' ? 'E-tray Analysis' :
           activeEvalType === 'ai-interview' ? 'AI Interview' :
           activeEvalType === 'ai-interview-v2' ? 'AI Interview V2' :
           activeEvalType === 'english-proficiency' ? 'English Proficiency' :
           'LoI Scoring'} Prompt Evaluations
        </h1>

        <div style={{ display: 'grid', gap: '20px', marginBottom: '30px' }}>
          {/* Dataset Manager for AI Interview, AI Interview V2, English Proficiency, and LoI */}
          {(activeEvalType === 'ai-interview' || activeEvalType === 'ai-interview-v2' || activeEvalType === 'english-proficiency' || activeEvalType === 'loi') && (
            <DatasetManager
              onDatasetSelect={setSelectedDatasetId}
              selectedDatasetId={selectedDatasetId}
              evaluationType={activeEvalType}
            />
          )}

          <div
            className="prompt-grid"
            style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
              gap: '20px'
            }}>
            {getFilteredPrompts().map(prompt => (
              <PromptEditor
                key={prompt.id}
                prompt={prompt}
                onUpdate={handlePromptUpdate}
              />
            ))}
          </div>

          {activeEvalType === 'idp' ? (
            <EvaluationRunner onRun={handleEvaluationRun} />
          ) : activeEvalType === 'lgd' ? (
            <LGDEvaluationRunner
              onRun={handleLGDEvaluationRun}
              onPollingUpdate={handleLGDPollingUpdate}
            />
          ) : activeEvalType === 'bei' ? (
            <BEIEvaluationRunner
              onRun={handleBEIEvaluationRun}
              onPollingUpdate={handleBEIPollingUpdate}
            />
          ) : activeEvalType === 'etray' ? (
            <EtrayEvaluationRunner
              onRun={handleEtrayEvaluationRun}
              onPollingUpdate={handleEtrayPollingUpdate}
            />
          ) : activeEvalType === 'ai-interview' ? (
            <AIInterviewEvaluationRunner
              selectedDatasetId={selectedDatasetId}
              onRun={handleAIInterviewEvaluationRun}
              onPollingUpdate={handleAIInterviewPollingUpdate}
            />
          ) : activeEvalType === 'ai-interview-v2' ? (
            <AIInterviewV2EvaluationRunner
              selectedDatasetId={selectedDatasetId}
              onRun={handleAIInterviewV2EvaluationRun}
              onPollingUpdate={handleAIInterviewV2PollingUpdate}
            />
          ) : activeEvalType === 'english-proficiency' ? (
            <EnglishProficiencyEvaluationRunner
              selectedDatasetId={selectedDatasetId}
              onRun={handleEnglishProficiencyEvaluationRun}
              onPollingUpdate={handleEnglishProficiencyPollingUpdate}
            />
          ) : (
            <LoIEvaluationRunner
              selectedDatasetId={selectedDatasetId}
              onRun={handleLoIEvaluationRun}
              onPollingUpdate={handleLoIPollingUpdate}
            />
          )}
        </div>

        {activeEvalType === 'idp' ? (
          <ResultsTable evaluations={evaluations} onEvaluationUpdate={handleEvaluationUpdate} />
        ) : activeEvalType === 'lgd' ? (
          <LGDResultsTable evaluations={lgdEvaluations} onEvaluationUpdate={handleLGDEvaluationUpdate} />
        ) : activeEvalType === 'bei' ? (
          <BEIResultsTable evaluations={beiEvaluations} onEvaluationUpdate={handleBEIEvaluationUpdate} />
        ) : activeEvalType === 'etray' ? (
          <EtrayResultsTable
            evaluations={etrayEvaluations}
            onAnnotationUpdate={async (id, annotation) => {
              await etrayEvaluationsApi.updateAnnotation(id, annotation);
              handleEtrayEvaluationUpdate({ ...etrayEvaluations.find(e => e.id === id), annotation });
            }}
          />
        ) : activeEvalType === 'ai-interview' ? (
          <AIInterviewResultsTable evaluations={aiInterviewEvaluations} onEvaluationUpdate={handleAIInterviewEvaluationUpdate} />
        ) : activeEvalType === 'ai-interview-v2' ? (
          <AIInterviewV2ResultsTable evaluations={aiInterviewV2Evaluations} onEvaluationUpdate={handleAIInterviewV2EvaluationUpdate} />
        ) : activeEvalType === 'english-proficiency' ? (
          <EnglishProficiencyResultsTable evaluations={englishProficiencyEvaluations} onEvaluationUpdate={handleEnglishProficiencyEvaluationUpdate} />
        ) : (
          <LoIResultsTable evaluations={loiEvaluations} />
        )}
      </div>
    </div>
  );
};

export default App;
